{"name": "farm-books", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "NODE_ENV=production vite build", "build:check": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "server": "node server/index.js", "dev:server": "nodemon server/index.js", "start": "concurrently \"npm run dev\" \"npm run dev:server\"", "start:prod": "node server/index.js", "setup-test-data": "node scripts/setup-test-data.js", "migrate:farm-grants": "node server/scripts/run_farm_grants_migration.js", "migrate:subscription-plans": "node server/scripts/run_add_missing_columns_to_subscription_plans.js", "migrate:dashboard-layouts": "node server/scripts/run_dashboard_layouts_columns_migration.js", "migrate:sessions-cascade": "node server/scripts/run_cascade_delete_to_sessions_migration.js", "migrate:customer-portal-enabled": "node server/scripts/run_customer_portal_enabled_migration.js", "fetch:grants": "node server/scripts/fetch_and_store_grants.js", "test": "mocha server/tests/**/*.test.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.798.0", "@aws-sdk/lib-storage": "^3.798.0", "@aws-sdk/s3-request-presigner": "^3.800.0", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@headlessui/react": "^2.2.2", "@heroicons/react": "^2.0.18", "lucide-react": "^0.514.0", "react-beautiful-dnd": "^13.1.1", "@lexical/code": "^0.31.0", "@lexical/html": "^0.31.0", "@lexical/link": "^0.31.0", "@lexical/list": "^0.31.0", "@lexical/react": "^0.31.0", "@lexical/rich-text": "^0.31.0", "@lexical/table": "^0.31.0", "@mui/material": "^7.0.2", "@react-google-maps/api": "^2.19.2", "@sentry/node": "^9.15.0", "@sentry/react": "^9.15.0", "@sentry/vite-plugin": "^3.3.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@tailwindcss/typography": "^0.5.10", "antd": "^5.24.9", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "chart.js": "^4.4.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "ethers": "^6.14.0", "express": "^4.18.2", "express-fileupload": "^1.4.3", "express-validator": "^7.2.1", "file-type": "^19.0.0", "googleapis": "^131.0.0", "gridstack": "^10.1.1", "helmet": "^7.1.0", "intuit-oauth": "^4.2.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "leo-profanity": "^1.7.0", "lexical": "^0.31.0", "marked": "^11.1.0", "matrix-js-sdk": "34.11.1", "morgan": "^1.10.0", "multer": "^2.0.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "node-forge": "^1.3.1", "nodemailer": "^6.9.7", "openai": "^4.97.0", "otplib": "^12.0.1", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "phosphor-react": "^1.4.1", "qrcode": "^1.5.4", "react": "^19.0.0", "react-bootstrap": "^2.10.1", "react-chartjs-2": "^5.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-dropzone": "^14.2.3", "react-head": "^3.4.2", "react-hook-form": "^7.49.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-pdf": "^9.2.1", "react-router-dom": "^6.22.0", "react-toastify": "^11.0.5", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.10", "recharts": "^2.15.3", "sequelize": "^6.35.1", "stripe": "^18.1.1", "tailwindcss": "^3.3.6", "twilio": "^4.23.0", "ua-parser-js": "^1.0.37", "uuid": "^11.1.0", "ws": "^8.18.2"}, "overrides": {"react-pdf": {"@types/react": "^19.0.10", "react": "^19.0.0", "react-dom": "^19.0.0"}, "react-beautiful-dnd": {"react": "^19.0.0", "react-dom": "^19.0.0", "@types/react": "^19.0.10"}}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/google.maps": "^3.54.10", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.14", "@types/react": "^19.0.10", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19.0.4", "@types/react-window": "^1.8.8", "@types/ua-parser-js": "^0.7.39", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "nodemon": "^3.0.2", "postcss": "^8.4.32", "terser": "^5.39.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}