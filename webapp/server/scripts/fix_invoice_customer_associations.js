#!/usr/bin/env node

/**
 * <PERSON>ript to fix invoice customer associations
 * This script identifies invoices that have customer_id = NULL and either:
 * 1. Associates them with an existing customer if there's only one customer for the farm
 * 2. Reports them for manual review if there are multiple customers
 */

import { sequelize } from '../config/database.js';
import Invoice from '../models/Invoice.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';

async function fixInvoiceCustomerAssociations() {
  try {
    console.log('Starting invoice customer association fix...');

    // Find all invoices with NULL customer_id that don't have recipient_farm_id
    // (farm-to-farm invoices don't need customer associations)
    const orphanedInvoices = await Invoice.findAll({
      where: {
        customer_id: null,
        recipient_farm_id: null
      },
      include: [
        {
          model: Farm,
          attributes: ['id', 'name']
        }
      ]
    });

    console.log(`Found ${orphanedInvoices.length} invoices without customer associations`);

    if (orphanedInvoices.length === 0) {
      console.log('No orphaned invoices found. All invoices have proper associations.');
      return;
    }

    // Group invoices by farm
    const invoicesByFarm = {};
    for (const invoice of orphanedInvoices) {
      const farmId = invoice.farm_id;
      if (!invoicesByFarm[farmId]) {
        invoicesByFarm[farmId] = [];
      }
      invoicesByFarm[farmId].push(invoice);
    }

    console.log(`Found orphaned invoices across ${Object.keys(invoicesByFarm).length} farms`);

    let fixedCount = 0;
    let manualReviewCount = 0;

    // Process each farm
    for (const [farmId, invoices] of Object.entries(invoicesByFarm)) {
      console.log(`\nProcessing farm ${farmId} with ${invoices.length} orphaned invoices...`);

      // Get all customers for this farm
      const customers = await Customer.findAll({
        where: { farm_id: farmId },
        attributes: ['id', 'name', 'email']
      });

      console.log(`Farm has ${customers.length} customers`);

      if (customers.length === 0) {
        console.log(`⚠️  Farm ${farmId} has no customers. These invoices need manual review.`);
        manualReviewCount += invoices.length;
        continue;
      }

      if (customers.length === 1) {
        // Only one customer - safe to associate all invoices with this customer
        const customer = customers[0];
        console.log(`✅ Associating ${invoices.length} invoices with customer: ${customer.name} (${customer.email})`);

        const transaction = await sequelize.transaction();
        try {
          await Invoice.update(
            { customer_id: customer.id },
            {
              where: {
                id: invoices.map(inv => inv.id)
              },
              transaction
            }
          );

          await transaction.commit();
          fixedCount += invoices.length;
          console.log(`✅ Successfully fixed ${invoices.length} invoices for farm ${farmId}`);
        } catch (error) {
          await transaction.rollback();
          console.error(`❌ Error fixing invoices for farm ${farmId}:`, error.message);
          manualReviewCount += invoices.length;
        }
      } else {
        // Multiple customers - need manual review
        console.log(`⚠️  Farm ${farmId} has ${customers.length} customers. These ${invoices.length} invoices need manual review:`);
        console.log('Customers:');
        customers.forEach(customer => {
          console.log(`  - ${customer.name} (${customer.email}) - ID: ${customer.id}`);
        });
        console.log('Invoices:');
        invoices.forEach(invoice => {
          console.log(`  - Invoice #${invoice.invoice_number} - ID: ${invoice.id}`);
        });
        manualReviewCount += invoices.length;
      }
    }

    console.log('\n=== SUMMARY ===');
    console.log(`Total orphaned invoices found: ${orphanedInvoices.length}`);
    console.log(`Automatically fixed: ${fixedCount}`);
    console.log(`Require manual review: ${manualReviewCount}`);

    if (manualReviewCount > 0) {
      console.log('\n⚠️  Some invoices require manual review. Please:');
      console.log('1. Review the invoices listed above');
      console.log('2. Determine the correct customer for each invoice');
      console.log('3. Update the customer_id manually in the database');
      console.log('\nExample SQL to fix manually:');
      console.log('UPDATE invoices SET customer_id = \'<customer-uuid>\' WHERE id = \'<invoice-uuid>\';');
    }

    console.log('\nInvoice customer association fix completed.');

  } catch (error) {
    console.error('Error fixing invoice customer associations:', error);
    process.exit(1);
  }
}

// Run the script if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  fixInvoiceCustomerAssociations()
    .then(() => {
      console.log('Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

export default fixInvoiceCustomerAssociations;
