#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to fix duplicate associations by removing them from individual model files
 * All associations should be defined only in associations.js
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const modelsDir = path.join(__dirname, '..', 'models');

// List of model files that likely have duplicate associations
const modelFiles = [
  'DeliverySlot.js',
  'DeliveryRoute.js', 
  'DeliveryAssignment.js',
  'DeliveryTracking.js',
  'FarmFulfillmentOptions.js',
  'DeliverySchedule.js',
  'PurchaseRequest.js',
  'ShoppingCart.js',
  'ShoppingCartItem.js'
];

const fixModelFile = (filename) => {
  const filePath = path.join(modelsDir, filename);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  File not found: ${filename}`);
    return;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;
  
  // Look for association definitions and replace them with a comment
  const associationPatterns = [
    // belongsTo patterns
    /^.*\.belongsTo\(.*\);?\s*$/gm,
    // hasMany patterns  
    /^.*\.hasMany\(.*\);?\s*$/gm,
    // hasOne patterns
    /^.*\.hasOne\(.*\);?\s*$/gm,
    // belongsToMany patterns
    /^.*\.belongsToMany\(.*\);?\s*$/gm
  ];
  
  let foundAssociations = false;
  
  // Find the start of association definitions
  const associationStartPattern = /\/\/ Define associations/;
  const associationStartMatch = content.match(associationStartPattern);
  
  if (associationStartMatch) {
    const startIndex = content.indexOf(associationStartMatch[0]);
    const beforeAssociations = content.substring(0, startIndex);
    const afterStart = content.substring(startIndex);
    
    // Find the export statement
    const exportPattern = /^export default/m;
    const exportMatch = afterStart.match(exportPattern);
    
    if (exportMatch) {
      const exportIndex = afterStart.indexOf(exportMatch[0]);
      const associationsSection = afterStart.substring(0, exportIndex);
      const afterExport = afterStart.substring(exportIndex);
      
      // Check if there are actual association calls in this section
      const hasAssociationCalls = associationPatterns.some(pattern => pattern.test(associationsSection));
      
      if (hasAssociationCalls) {
        foundAssociations = true;
        // Replace the associations section with a comment
        const newAssociationsSection = `// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

`;
        content = beforeAssociations + newAssociationsSection + afterExport;
      }
    }
  }
  
  if (foundAssociations) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Fixed associations in ${filename}`);
  } else {
    console.log(`ℹ️  No associations found in ${filename}`);
  }
};

const main = () => {
  console.log('🔧 Starting duplicate association fix...');
  console.log(`📁 Models directory: ${modelsDir}`);
  
  modelFiles.forEach(filename => {
    console.log(`\n🔍 Processing ${filename}...`);
    fixModelFile(filename);
  });
  
  console.log('\n🎉 Duplicate association fix completed!');
  console.log('💡 All associations should now be defined only in associations.js');
};

main();
