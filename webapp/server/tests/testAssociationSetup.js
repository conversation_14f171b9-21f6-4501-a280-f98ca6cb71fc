#!/usr/bin/env node

/**
 * Test script to verify association setup without database connection
 */

import Invoice from '../models/Invoice.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import InvoiceItem from '../models/InvoiceItem.js';
import { setupAssociations, verifyAssociations } from '../models/associations.js';

const testAssociationSetup = () => {
  try {
    console.log('🔧 Testing association setup...');
    
    // Check models are loaded
    console.log('\n📋 Model Loading Check:');
    console.log('Invoice model loaded:', !!Invoice);
    console.log('Customer model loaded:', !!Customer);
    console.log('Farm model loaded:', !!Farm);
    console.log('InvoiceItem model loaded:', !!InvoiceItem);
    
    // Check initial associations (should be empty or minimal)
    console.log('\n📋 Initial Associations:');
    console.log('Invoice associations before setup:', Object.keys(Invoice.associations || {}));
    console.log('Customer associations before setup:', Object.keys(Customer.associations || {}));
    
    // Set up associations
    console.log('\n🔧 Setting up associations...');
    setupAssociations();
    
    // Check associations after setup
    console.log('\n📋 Final Associations:');
    console.log('Invoice associations after setup:', Object.keys(Invoice.associations || {}));
    console.log('Customer associations after setup:', Object.keys(Customer.associations || {}));
    
    // Verify specific associations
    console.log('\n🔍 Specific Association Checks:');
    const invoiceCustomerAssoc = Invoice.associations?.Customer;
    const customerInvoicesAssoc = Customer.associations?.Invoices;
    
    console.log('Invoice -> Customer association:', invoiceCustomerAssoc ? '✅ Found' : '❌ Missing');
    if (invoiceCustomerAssoc) {
      console.log('  - Type:', invoiceCustomerAssoc.associationType);
      console.log('  - Foreign Key:', invoiceCustomerAssoc.foreignKey);
      console.log('  - Target:', invoiceCustomerAssoc.target.name);
    }
    
    console.log('Customer -> Invoices association:', customerInvoicesAssoc ? '✅ Found' : '❌ Missing');
    if (customerInvoicesAssoc) {
      console.log('  - Type:', customerInvoicesAssoc.associationType);
      console.log('  - Foreign Key:', customerInvoicesAssoc.foreignKey);
      console.log('  - Target:', customerInvoicesAssoc.target.name);
    }
    
    // Check other important associations
    console.log('\n🔍 Other Important Associations:');
    const invoiceItemsAssoc = Invoice.associations?.InvoiceItems;
    const invoiceItemAssoc = InvoiceItem.associations?.invoice;
    
    console.log('Invoice -> InvoiceItems association:', invoiceItemsAssoc ? '✅ Found' : '❌ Missing');
    console.log('InvoiceItem -> Invoice association:', invoiceItemAssoc ? '✅ Found' : '❌ Missing');
    
    // Summary
    console.log('\n📊 Summary:');
    const criticalAssociationsWorking = invoiceCustomerAssoc && customerInvoicesAssoc;
    console.log('Critical Customer-Invoice associations:', criticalAssociationsWorking ? '✅ Working' : '❌ Broken');
    
    if (criticalAssociationsWorking) {
      console.log('🎉 Association setup appears to be working correctly!');
      console.log('💡 If you\'re still getting association errors, the issue might be:');
      console.log('   - Timing: Associations not set up before queries are executed');
      console.log('   - Database schema: Foreign key constraints or table structure');
      console.log('   - Model synchronization: Models not properly synced with database');
    } else {
      console.log('❌ Association setup is not working correctly.');
      console.log('💡 Possible issues:');
      console.log('   - Model imports are not working properly');
      console.log('   - Circular dependency issues');
      console.log('   - setupAssociations() not being called');
    }
    
  } catch (error) {
    console.error('❌ Error during association test:', error);
    console.error('Stack trace:', error.stack);
  }
};

// Run the test
console.log('🚀 Starting association setup test...');
testAssociationSetup();
console.log('✅ Association setup test completed.');
