
import Farm from './Farm.js';
import User from './User.js';
import UserFarm from './UserFarm.js';
import InventoryItem from './InventoryItem.js';
import InventoryCategory from './InventoryCategory.js';
import InventoryTransaction from './InventoryTransaction.js';
import Product from './Product.js';
import SeedProduct from './SeedProduct.js';
import ChemicalProduct from './ChemicalProduct.js';
import Equipment from './Equipment.js';
import EquipmentSharing from './EquipmentSharing.js';
import EquipmentTelematics from './EquipmentTelematics.js';
import Document from './Document.js';
import DocumentFolder from './DocumentFolder.js';
import DocumentPermission from './DocumentPermission.js';
import Livestock from './Livestock.js';
import LivestockGroup from './LivestockGroup.js';
import Crop from './Crop.js';
import CropActivity from './CropActivity.js';
import IoTDevice from './IoTDevice.js';
import IoTData from './IoTData.js';
import Integration from './Integration.js';
import Role from './Role.js';
import RolePermission from './RolePermission.js';
import Supplier from './Supplier.js';
import Order from './Order.js';
import OrderItem from './OrderItem.js';
import ServiceProvider from './ServiceProvider.js';
import ServiceRequest from './ServiceRequest.js';
import Vet from './Vet.js';
import Vendor from './Vendor.js';
import Field from './Field.js';
import SupportTicket from './SupportTicket.js';
import SupportTicketComment from './SupportTicketComment.js';
import SupportTicketAttachment from './SupportTicketAttachment.js';
import HelpGuide from './HelpGuide.js';
import HelpTip from './HelpTip.js';
import UserHelpTipDismissal from './UserHelpTipDismissal.js';
import PasswordGroup from './PasswordGroup.js';
import Password from './Password.js';
import PasswordGroupPermission from './PasswordGroupPermission.js';
import UserRecoveryKey from './UserRecoveryKey.js';

import MigrationSystem from './MigrationSystem.js';
import MigrationJob from './MigrationJob.js';
import MigrationResult from './MigrationResult.js';
import DatabaseMigration from './DatabaseMigration.js';
import Receipt from './Receipt.js';
import Driver from './Driver.js';
import Delivery from './Delivery.js';
import Pickup from './Pickup.js';
import DriverSchedule from './DriverSchedule.js';
import DriverLocation from './DriverLocation.js';
import Customer from './Customer.js';
import Expense from './Expense.js';
import CropType from './CropType.js';
import Harvest from './Harvest.js';
import MarketContract from './MarketContract.js';
import PriceComparison from './PriceComparison.js';
import MarketTrend from './MarketTrend.js';
import MarketplaceListing from './MarketplaceListing.js';
import SupplierProduct from "./SupplierProduct.js";
import SupplierReview from "./SupplierReview.js";
import DashboardLayout from './DashboardLayout.js';
import FarmDashboardLayout from './FarmDashboardLayout.js';
import GlobalDashboardLayout from './GlobalDashboardLayout.js';
import Alert from './Alert.js';
import AlertRule from './AlertRule.js';
import SubscriptionPlan from './SubscriptionPlan.js';
import Employee from './Employee.js';
import MarketPrice from './MarketPrice.js';
import FuturePrice from './FuturePrice.js';
import HistoricalPrice from './HistoricalPrice.js';
import HarvestDirectionMap from './HarvestDirectionMap.js';
import TaxCategory from './TaxCategory.js';
import TaxDeduction from './TaxDeduction.js';
import TaxDocument from './TaxDocument.js';
import EmployeeTaxInfo from './EmployeeTaxInfo.js';
import ContractorTaxInfo from './ContractorTaxInfo.js';
import TaxPayment from './TaxPayment.js';
import TaxFiling from './TaxFiling.js';
import SignableDocument from './SignableDocument.js';
import DocumentSigner from './DocumentSigner.js';
import DocumentSignature from './DocumentSignature.js';
import DocumentField from './DocumentField.js';
import DocumentAuditLog from './DocumentAuditLog.js';
import DocumentBlockchainVerification from './DocumentBlockchainVerification.js';
import DigitalCertificate from './DigitalCertificate.js';
import Invoice from './Invoice.js';
import InvoiceItem from './InvoiceItem.js';
import InvoiceQuestion from './InvoiceQuestion.js';
import InvoiceDispute from './InvoiceDispute.js';
import InvoiceDisputeMessage from './InvoiceDisputeMessage.js';
import InvoiceAuditLog from './InvoiceAuditLog.js';
import InvoiceNotification from './InvoiceNotification.js';
import Transaction from './Transaction.js';
import FarmFieldCollaboration from './FarmFieldCollaboration.js';
import CustomerNotification from './CustomerNotification.js';
import CustomerAddress from './CustomerAddress.js';
import BillCategory from './BillCategory.js';
import Bill from './Bill.js';
import RecurringBill from './RecurringBill.js';
import BillPayment from './BillPayment.js';
import BillTransaction from './BillTransaction.js';
import BillAttachment from './BillAttachment.js';
import FarmAssociation from './FarmAssociation.js';
import ShoppingCart from './ShoppingCart.js';
import ShoppingCartItem from './ShoppingCartItem.js';
import PurchaseRequest from './PurchaseRequest.js';
import PurchaseRequestItem from './PurchaseRequestItem.js';
import FarmFulfillmentOptions from './FarmFulfillmentOptions.js';
import DeliverySchedule from './DeliverySchedule.js';
import DeliverySlot from './DeliverySlot.js';
import DeliveryRoute from './DeliveryRoute.js';
import DeliveryAssignment from './DeliveryAssignment.js';
import DeliveryTracking from './DeliveryTracking.js';
import FarmFeatureToggles from './FarmFeatureToggles.js';
import ProductImage from './ProductImage.js';

// Set up associations that require all models to be loaded first
export const setupAssociations = () => {
  // ============================================================================
  // CORE SYSTEM ASSOCIATIONS (User, Farm, UserFarm, Roles, Subscriptions)
  // ============================================================================

  // Farm associations
  FarmAssociation.belongsTo(Farm, { foreignKey: 'initiator_farm_id', as: 'initiatorFarm' });
  FarmAssociation.belongsTo(Farm, { foreignKey: 'associated_farm_id', as: 'associatedFarm' });
  Farm.hasMany(FarmAssociation, { foreignKey: 'initiator_farm_id', as: 'initiatedAssociations' });
  Farm.hasMany(FarmAssociation, { foreignKey: 'associated_farm_id', as: 'receivedAssociations' });

  // User-Farm many-to-many relationship through UserFarm
  User.belongsToMany(Farm, { through: UserFarm, foreignKey: 'user_id' });
  Farm.belongsToMany(User, { through: UserFarm, foreignKey: 'farm_id' });

  // Direct associations with UserFarm junction table
  Farm.hasMany(UserFarm, { foreignKey: 'farm_id', as: 'userFarms' });
  UserFarm.belongsTo(Farm, { foreignKey: 'farm_id' });
  User.hasMany(UserFarm, { foreignKey: 'user_id', as: 'userFarms' });
  UserFarm.belongsTo(User, { foreignKey: 'user_id' });

  // Role system associations
  UserFarm.belongsTo(Role, { foreignKey: 'role_id' });
  Role.hasMany(UserFarm, { foreignKey: 'role_id', as: 'userFarms' });

  // Subscription plan associations
  User.belongsTo(SubscriptionPlan, { foreignKey: 'subscription_plan_id' });
  SubscriptionPlan.hasMany(User, { foreignKey: 'subscription_plan_id' });
  Farm.belongsTo(SubscriptionPlan, { foreignKey: 'subscription_plan_id' });
  SubscriptionPlan.hasMany(Farm, { foreignKey: 'subscription_plan_id' });

  // Employee management associations
  Employee.belongsTo(Farm, { foreignKey: 'farm_id' });
  Employee.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  Farm.hasMany(Employee, { foreignKey: 'farm_id', as: 'employees' });
  User.hasMany(Employee, { foreignKey: 'user_id', as: 'employeeRoles', onDelete: 'CASCADE' });

  // ============================================================================
  // INVENTORY & PRODUCT MANAGEMENT
  // ============================================================================

  // Inventory category and item relationships
  InventoryCategory.belongsTo(Farm, { foreignKey: 'farm_id', as: 'inventoryCategoryFarm' });
  Farm.hasMany(InventoryCategory, { foreignKey: 'farm_id', as: 'categories' });

  InventoryItem.belongsTo(InventoryCategory, { foreignKey: 'category_id', as: 'category' });
  InventoryCategory.hasMany(InventoryItem, { foreignKey: 'category_id', as: 'items' });
  InventoryItem.belongsTo(Farm, { foreignKey: 'farm_id', as: 'inventoryItemFarm' });
  Farm.hasMany(InventoryItem, { foreignKey: 'farm_id', as: 'inventoryItems' });

  // Inventory transaction tracking
  InventoryItem.hasMany(InventoryTransaction, { foreignKey: 'inventory_item_id', as: 'transactions' });
  InventoryTransaction.belongsTo(InventoryItem, { foreignKey: 'inventory_item_id', as: 'item' });
  InventoryTransaction.belongsTo(User, { foreignKey: 'user_id' });
  User.hasMany(InventoryTransaction, { foreignKey: 'user_id', as: 'inventoryTransactions' });

  // Product management
  Product.belongsTo(Farm, { foreignKey: 'farm_id', as: 'productFarm' });
  Farm.hasMany(Product, { foreignKey: 'farm_id', as: 'products' });

  // Product images
  Product.hasMany(ProductImage, { foreignKey: 'product_id', as: 'images' });
  ProductImage.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });

  // Specialized product types
  SeedProduct.belongsTo(Product, { foreignKey: 'product_id', as: 'seedProduct' });
  Product.hasOne(SeedProduct, { foreignKey: 'product_id' });
  SeedProduct.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(SeedProduct, { foreignKey: 'farm_id' });

  ChemicalProduct.belongsTo(Product, { foreignKey: 'product_id', as: 'chemicalProduct' });
  Product.hasOne(ChemicalProduct, { foreignKey: 'product_id' });
  ChemicalProduct.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(ChemicalProduct, { foreignKey: 'farm_id' });

  // ============================================================================
  // EQUIPMENT MANAGEMENT
  // ============================================================================

  // Equipment ownership and tracking
  Equipment.belongsTo(Farm, { foreignKey: 'farm_id', as: 'equipmentFarm' });
  Farm.hasMany(Equipment, { foreignKey: 'farm_id', as: 'equipment' });

  // Equipment telematics data
  Equipment.hasMany(EquipmentTelematics, { foreignKey: 'equipment_id', as: 'telematics' });
  EquipmentTelematics.belongsTo(Equipment, { foreignKey: 'equipment_id', as: 'telematicsEquipment' });

  // Equipment sharing between farms
  EquipmentSharing.belongsTo(Equipment, { foreignKey: 'equipment_id', as: 'sharingEquipment' });
  Equipment.hasMany(EquipmentSharing, { foreignKey: 'equipment_id', as: 'sharing' });
  EquipmentSharing.belongsTo(Farm, { as: 'Owner', foreignKey: 'owner_farm_id' });
  EquipmentSharing.belongsTo(Farm, { as: 'Renter', foreignKey: 'renter_farm_id' });
  Farm.hasMany(EquipmentSharing, { as: 'EquipmentLentOut', foreignKey: 'owner_farm_id' });
  Farm.hasMany(EquipmentSharing, { as: 'EquipmentBorrowed', foreignKey: 'renter_farm_id' });

  // ============================================================================
  // DOCUMENT MANAGEMENT SYSTEM
  // ============================================================================

  // Document folder structure
  DocumentFolder.belongsTo(Farm, { foreignKey: 'farm_id', as: 'documentFolderFarm' });
  DocumentFolder.hasMany(Document, { foreignKey: 'folder_id', as: 'folderDocuments' });

  // Document ownership and organization
  Document.belongsTo(User, { foreignKey: 'uploaded_by', as: 'uploader', onDelete: 'CASCADE' });
  Document.belongsTo(Farm, { foreignKey: 'farm_id', as: 'documentFarm' });
  Document.belongsTo(DocumentFolder, { foreignKey: 'folder_id', as: 'documentFolder' });

  // Document permissions and access control
  DocumentPermission.belongsTo(Document, { foreignKey: 'document_id', as: 'permissionDocument' });
  DocumentPermission.belongsTo(User, { foreignKey: 'user_id', as: 'permissionUser', onDelete: 'CASCADE' });
  Document.hasMany(DocumentPermission, { foreignKey: 'document_id', as: 'permissions' });
  User.hasMany(DocumentPermission, { foreignKey: 'user_id', as: 'documentPermissions', onDelete: 'CASCADE' });

  // ============================================================================
  // FARM OPERATIONS (Livestock, Crops, Fields, Harvests)
  // ============================================================================

  // Field management
  Field.belongsTo(Farm, { foreignKey: 'farm_id', as: 'fieldFarm' });
  Farm.hasMany(Field, { foreignKey: 'farm_id', as: 'fields' });

  // Field collaboration between farms
  FarmFieldCollaboration.belongsTo(Field, { foreignKey: 'field_id', as: 'field' });
  FarmFieldCollaboration.belongsTo(Farm, { foreignKey: 'owner_farm_id', as: 'ownerFarm' });
  FarmFieldCollaboration.belongsTo(Farm, { foreignKey: 'collaborator_farm_id', as: 'collaboratorFarm' });
  Field.hasMany(FarmFieldCollaboration, { foreignKey: 'field_id', as: 'collaborations' });
  Farm.hasMany(FarmFieldCollaboration, { foreignKey: 'owner_farm_id', as: 'sharedFields' });
  Farm.hasMany(FarmFieldCollaboration, { foreignKey: 'collaborator_farm_id', as: 'collaboratingFields' });

  // Livestock management
  Livestock.belongsTo(Farm, { foreignKey: 'farm_id', as: 'livestockFarm' });
  Farm.hasMany(Livestock, { foreignKey: 'farm_id', as: 'livestock' });
  LivestockGroup.belongsTo(Farm, { foreignKey: 'farm_id', as: 'livestockGroupFarm' });
  Farm.hasMany(LivestockGroup, { foreignKey: 'farm_id', as: 'livestockGroups' });

  // Crop management and types
  CropType.belongsTo(Farm, { foreignKey: 'farm_id', as: 'cropTypeFarm' });
  Farm.hasMany(CropType, { foreignKey: 'farm_id', as: 'cropTypes' });
  Crop.belongsTo(Farm, { foreignKey: 'farm_id', as: 'cropFarm' });
  Farm.hasMany(Crop, { foreignKey: 'farm_id', as: 'crops' });

  // Crop activities and tracking
  Crop.hasMany(CropActivity, { foreignKey: 'crop_id', as: 'activities' });
  CropActivity.belongsTo(Crop, { foreignKey: 'crop_id', as: 'crop' });

  // Harvest management
  Harvest.belongsTo(Farm, { foreignKey: 'farm_id', as: 'harvestFarm' });
  Farm.hasMany(Harvest, { foreignKey: 'farm_id', as: 'harvests' });
  Harvest.belongsTo(Field, { foreignKey: 'field_id', as: 'harvestField' });
  Field.hasMany(Harvest, { foreignKey: 'field_id', as: 'harvests' });
  Harvest.belongsTo(Crop, { foreignKey: 'crop_id', as: 'harvestCrop' });
  Crop.hasMany(Harvest, { foreignKey: 'crop_id', as: 'harvests' });

  // Harvest direction mapping
  HarvestDirectionMap.belongsTo(Field, { foreignKey: 'field_id', as: 'field' });
  Field.hasMany(HarvestDirectionMap, { foreignKey: 'field_id', as: 'harvestDirectionMaps' });

  // ============================================================================
  // IOT & TECHNOLOGY INTEGRATIONS
  // ============================================================================

  // IoT device management and data collection
  IoTDevice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'iotDeviceFarm' });
  Farm.hasMany(IoTDevice, { foreignKey: 'farm_id', as: 'iotDevices' });
  IoTDevice.hasMany(IoTData, { foreignKey: 'device_id', as: 'data' });
  IoTData.belongsTo(IoTDevice, { foreignKey: 'device_id', as: 'device' });

  // Third-party integrations
  Integration.belongsTo(Farm, { foreignKey: 'farm_id', as: 'integrationFarm' });
  Farm.hasMany(Integration, { foreignKey: 'farm_id', as: 'integrations' });

  // ============================================================================
  // ALERTS & NOTIFICATIONS
  // ============================================================================

  // Alert system
  Alert.belongsTo(Farm, { foreignKey: 'farm_id' });
  Alert.belongsTo(User, { foreignKey: 'user_id' });
  Farm.hasMany(Alert, { foreignKey: 'farm_id', as: 'alerts' });
  User.hasMany(Alert, { foreignKey: 'user_id', as: 'alerts' });

  // Alert rule configuration
  AlertRule.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(AlertRule, { foreignKey: 'farm_id', as: 'alertRules' });

  // ============================================================================
  // PERMISSIONS & ACCESS CONTROL
  // ============================================================================

  // Role-based permissions
  RolePermission.belongsTo(Farm, { foreignKey: 'farm_id', as: 'rolePermissionFarm' });
  Farm.hasMany(RolePermission, { foreignKey: 'farm_id', as: 'farmRolePermissions' });

  // ============================================================================
  // SUPPLY CHAIN & VENDOR MANAGEMENT
  // ============================================================================

  // Supplier management
  Supplier.belongsTo(Farm, { foreignKey: 'farm_id', as: 'supplierFarm' });
  Farm.hasMany(Supplier, { foreignKey: 'farm_id', as: 'suppliers' });
  Supplier.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(Supplier, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // Supplier product catalog
  SupplierProduct.belongsTo(Supplier, { foreignKey: 'supplier_id' });
  Supplier.hasMany(SupplierProduct, { foreignKey: 'supplier_id' });
  SupplierProduct.belongsTo(Product, { foreignKey: 'product_id' });
  Product.hasMany(SupplierProduct, { foreignKey: 'product_id' });

  // Supplier reviews and ratings
  SupplierReview.belongsTo(Supplier, { foreignKey: 'supplier_id' });
  Supplier.hasMany(SupplierReview, { foreignKey: 'supplier_id' });
  SupplierReview.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(SupplierReview, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // Order management
  Order.belongsTo(Farm, { foreignKey: 'farm_id', as: 'orderFarm' });
  Order.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'orderSupplier' });
  Order.belongsTo(User, { foreignKey: 'user_id', as: 'creator', onDelete: 'CASCADE' });
  Farm.hasMany(Order, { foreignKey: 'farm_id', as: 'orders' });
  Supplier.hasMany(Order, { foreignKey: 'supplier_id', as: 'supplierOrders' });
  User.hasMany(Order, { foreignKey: 'user_id', as: 'createdOrders', onDelete: 'CASCADE' });

  // Order items and inventory linking
  Order.hasMany(OrderItem, { foreignKey: 'order_id', as: 'items' });
  OrderItem.belongsTo(Order, { foreignKey: 'order_id', as: 'orderItemOrder' });
  OrderItem.belongsTo(InventoryItem, { foreignKey: 'inventory_item_id' });
  InventoryItem.hasMany(OrderItem, { foreignKey: 'inventory_item_id' });

  // Service provider management
  ServiceProvider.belongsTo(Farm, { foreignKey: 'farm_id', as: 'serviceProviderFarm' });
  Farm.hasMany(ServiceProvider, { foreignKey: 'farm_id', as: 'serviceProviders' });
  ServiceRequest.belongsTo(Farm, { foreignKey: 'farm_id', as: 'serviceRequestFarm' });
  ServiceRequest.belongsTo(ServiceProvider, { foreignKey: 'provider_id', as: 'provider' });
  Farm.hasMany(ServiceRequest, { foreignKey: 'farm_id', as: 'serviceRequests' });
  ServiceProvider.hasMany(ServiceRequest, { foreignKey: 'provider_id', as: 'providerServiceRequests' });

  // Veterinary services
  Vet.belongsTo(Farm, { foreignKey: 'farm_id', as: 'vetFarm' });
  Farm.hasMany(Vet, { foreignKey: 'farm_id', as: 'vets' });
  Vet.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(Vet, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // Vendor management
  Vendor.belongsTo(Farm, { foreignKey: 'farm_id', as: 'vendorFarm' });
  Farm.hasMany(Vendor, { foreignKey: 'farm_id', as: 'vendors' });
  Vendor.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(Vendor, { foreignKey: 'user_id', onDelete: 'CASCADE' });

  // ============================================================================
  // SUPPORT & HELP SYSTEM
  // ============================================================================

  // Support ticket management
  SupportTicket.belongsTo(User, { foreignKey: 'user_id', as: 'creator', onDelete: 'CASCADE' });
  SupportTicket.belongsTo(User, { foreignKey: 'assigned_to', as: 'assignee', onDelete: 'CASCADE' });
  User.hasMany(SupportTicket, { foreignKey: 'assigned_to', as: 'assignedTickets', onDelete: 'CASCADE' });
  SupportTicket.belongsTo(Farm, { foreignKey: 'farm_id', as: 'supportTicketFarm' });
  Farm.hasMany(SupportTicket, { foreignKey: 'farm_id' });

  // Support ticket comments
  SupportTicketComment.belongsTo(SupportTicket, { foreignKey: 'ticket_id' });
  SupportTicket.hasMany(SupportTicketComment, { foreignKey: 'ticket_id', as: 'comments' });
  SupportTicketComment.belongsTo(User, { foreignKey: 'user_id', as: 'author', onDelete: 'CASCADE' });
  User.hasMany(SupportTicketComment, { foreignKey: 'user_id', as: 'ticketComments', onDelete: 'CASCADE' });

  // Support ticket attachments
  SupportTicketAttachment.belongsTo(SupportTicket, { foreignKey: 'ticket_id' });
  SupportTicket.hasMany(SupportTicketAttachment, { foreignKey: 'ticket_id', as: 'attachments' });
  SupportTicketAttachment.belongsTo(User, { foreignKey: 'user_id', as: 'uploader', onDelete: 'CASCADE' });
  User.hasMany(SupportTicketAttachment, { foreignKey: 'user_id', as: 'uploadedAttachments', onDelete: 'CASCADE' });

  // Help tip system
  UserHelpTipDismissal.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasMany(UserHelpTipDismissal, { foreignKey: 'user_id', as: 'dismissedHelpTips', onDelete: 'CASCADE' });
  UserHelpTipDismissal.belongsTo(HelpTip, { foreignKey: 'help_tip_id' });
  HelpTip.hasMany(UserHelpTipDismissal, { foreignKey: 'help_tip_id', as: 'userDismissals' });

  // ============================================================================
  // DATA MIGRATION & SYSTEM ADMINISTRATION
  // ============================================================================

  // Data migration system
  MigrationSystem.hasMany(MigrationJob, { foreignKey: 'source_system', as: 'jobs' });
  MigrationJob.belongsTo(User, { foreignKey: 'user_id', as: 'migrationJobUser', onDelete: 'CASCADE' });
  MigrationJob.belongsTo(Farm, { foreignKey: 'farm_id', as: 'migrationJobFarm' });
  MigrationJob.belongsTo(MigrationSystem, { foreignKey: 'source_system', as: 'system' });
  Farm.hasMany(MigrationJob, { foreignKey: 'farm_id', as: 'farmMigrationJobs' });
  User.hasMany(MigrationJob, { foreignKey: 'user_id', as: 'userMigrationJobs', onDelete: 'CASCADE' });

  // Migration results tracking
  MigrationResult.belongsTo(MigrationJob, { foreignKey: 'job_id', as: 'job' });
  MigrationJob.hasOne(MigrationResult, { foreignKey: 'job_id', as: 'result' });

  // Database schema migrations
  DatabaseMigration.belongsTo(User, { foreignKey: 'applied_by', as: 'appliedBy', onDelete: 'CASCADE' });
  User.hasMany(DatabaseMigration, { foreignKey: 'applied_by', as: 'appliedMigrations', onDelete: 'CASCADE' });

  // Dashboard customization
  DashboardLayout.belongsTo(User, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  User.hasOne(DashboardLayout, { foreignKey: 'user_id', onDelete: 'CASCADE' });
  FarmDashboardLayout.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasOne(FarmDashboardLayout, { foreignKey: 'farm_id' });

  // ============================================================================
  // FINANCIAL MANAGEMENT
  // ============================================================================

  // Expense tracking and receipts
  Expense.belongsTo(Employee, { foreignKey: 'employee_id' });
  Expense.belongsTo(User, { foreignKey: 'reviewed_by', as: 'reviewer', onDelete: 'CASCADE' });
  Employee.hasMany(Expense, { foreignKey: 'employee_id', as: 'expenses' });
  User.hasMany(Expense, { foreignKey: 'reviewed_by', as: 'reviewedExpenses', onDelete: 'CASCADE' });

  // Receipt management
  Receipt.belongsTo(User, { foreignKey: 'uploaded_by', as: 'uploader', onDelete: 'CASCADE' });
  User.hasMany(Receipt, { foreignKey: 'uploaded_by', as: 'uploadedReceipts', onDelete: 'CASCADE' });
  Receipt.belongsTo(Farm, { foreignKey: 'farm_id', as: 'receiptFarm' });
  Farm.hasMany(Receipt, { foreignKey: 'farm_id', as: 'receipts' });
  Receipt.belongsTo(Expense, { foreignKey: 'expense_id', as: 'expense' });
  Expense.hasOne(Receipt, { foreignKey: 'expense_id', as: 'receipt' });

  // ============================================================================
  // TRANSPORT MANAGEMENT SYSTEM
  // ============================================================================

  // Driver management
  Driver.belongsTo(Farm, { foreignKey: 'farm_id', as: 'driverFarm' });
  Driver.belongsTo(User, { foreignKey: 'user_id', as: 'driverUser', onDelete: 'CASCADE' });
  Farm.hasMany(Driver, { foreignKey: 'farm_id', as: 'drivers' });
  User.hasOne(Driver, { foreignKey: 'user_id', as: 'driverUser', onDelete: 'CASCADE' });

  // Customer management
  Customer.belongsTo(Farm, { foreignKey: 'farm_id', as: 'customerFarm' });
  Farm.hasMany(Customer, { foreignKey: 'farm_id', as: 'customers' });

  // Customer addresses
  CustomerAddress.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  CustomerAddress.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Customer.hasMany(CustomerAddress, { foreignKey: 'customer_id', as: 'addresses' });
  Farm.hasMany(CustomerAddress, { foreignKey: 'farm_id', as: 'customerAddresses' });

  // Delivery operations
  Delivery.belongsTo(Farm, { foreignKey: 'farm_id', as: 'deliveryFarm' });
  Delivery.belongsTo(Driver, { foreignKey: 'driver_id', as: 'driver' });
  Delivery.belongsTo(Customer, { foreignKey: 'customer_id', as: 'deliveryCustomer' });
  Delivery.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
  Farm.hasMany(Delivery, { foreignKey: 'farm_id', as: 'deliveries' });
  Driver.hasMany(Delivery, { foreignKey: 'driver_id', as: 'deliveries' });
  Customer.hasMany(Delivery, { foreignKey: 'customer_id', as: 'deliveries' });
  Order.hasOne(Delivery, { foreignKey: 'order_id', as: 'delivery' });

  // Pickup operations
  Pickup.belongsTo(Farm, { foreignKey: 'farm_id', as: 'pickupFarm' });
  Pickup.belongsTo(Driver, { foreignKey: 'driver_id', as: 'pickupDriver' });
  Pickup.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'supplier' });
  Pickup.belongsTo(Customer, { foreignKey: 'customer_id', as: 'pickupCustomer' });
  Pickup.belongsTo(Order, { foreignKey: 'order_id', as: 'order' });
  Farm.hasMany(Pickup, { foreignKey: 'farm_id', as: 'pickups' });
  Driver.hasMany(Pickup, { foreignKey: 'driver_id', as: 'pickups' });
  Supplier.hasMany(Pickup, { foreignKey: 'supplier_id', as: 'pickups' });
  Customer.hasMany(Pickup, { foreignKey: 'customer_id', as: 'pickups' });
  Order.hasOne(Pickup, { foreignKey: 'order_id', as: 'pickup' });

  // Driver scheduling and tracking
  DriverSchedule.belongsTo(Farm, { foreignKey: 'farm_id', as: 'driverScheduleFarm' });
  DriverSchedule.belongsTo(Driver, { foreignKey: 'driver_id', as: 'driver' });
  DriverSchedule.belongsTo(Delivery, { foreignKey: 'delivery_id', as: 'delivery' });
  DriverSchedule.belongsTo(Pickup, { foreignKey: 'pickup_id', as: 'pickup' });
  Farm.hasMany(DriverSchedule, { foreignKey: 'farm_id', as: 'driverSchedules' });
  Driver.hasMany(DriverSchedule, { foreignKey: 'driver_id', as: 'schedules' });
  Delivery.hasOne(DriverSchedule, { foreignKey: 'delivery_id', as: 'deliverySchedule' });
  Pickup.hasOne(DriverSchedule, { foreignKey: 'pickup_id', as: 'pickup' });

  // Driver location tracking
  DriverLocation.belongsTo(Farm, { foreignKey: 'farm_id', as: 'driverLocationFarm' });
  DriverLocation.belongsTo(Driver, { foreignKey: 'driver_id', as: 'locationDriver' });
  Farm.hasMany(DriverLocation, { foreignKey: 'farm_id', as: 'driverLocations' });
  Driver.hasMany(DriverLocation, { foreignKey: 'driver_id', as: 'locations' });

  // Delivery scheduling system
  DeliverySchedule.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Farm.hasMany(DeliverySchedule, { foreignKey: 'farm_id', as: 'delivery_schedules' });

  DeliverySlot.belongsTo(DeliverySchedule, { foreignKey: 'schedule_id', as: 'schedule' });
  DeliverySchedule.hasMany(DeliverySlot, { foreignKey: 'schedule_id', as: 'slots' });

  DeliveryRoute.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  DeliveryRoute.belongsTo(User, { foreignKey: 'driver_id', as: 'driver' });
  Farm.hasMany(DeliveryRoute, { foreignKey: 'farm_id', as: 'delivery_routes' });
  User.hasMany(DeliveryRoute, { foreignKey: 'driver_id', as: 'assigned_routes' });

  DeliveryAssignment.belongsTo(DeliveryRoute, { foreignKey: 'route_id', as: 'route' });
  DeliveryAssignment.belongsTo(PurchaseRequest, { foreignKey: 'purchase_request_id', as: 'purchase_request' });
  DeliveryRoute.hasMany(DeliveryAssignment, { foreignKey: 'route_id', as: 'assignments' });
  PurchaseRequest.hasOne(DeliveryAssignment, { foreignKey: 'purchase_request_id', as: 'delivery_assignment' });

  DeliveryTracking.belongsTo(DeliveryRoute, { foreignKey: 'route_id', as: 'route' });
  DeliveryRoute.hasMany(DeliveryTracking, { foreignKey: 'route_id', as: 'tracking_points' });

  // Add delivery slot and route references to purchase requests
  PurchaseRequest.belongsTo(DeliverySlot, { foreignKey: 'delivery_slot_id', as: 'delivery_slot' });
  DeliverySlot.hasMany(PurchaseRequest, { foreignKey: 'delivery_slot_id', as: 'purchase_requests' });

  PurchaseRequest.belongsTo(DeliveryRoute, { foreignKey: 'delivery_route_id', as: 'delivery_route' });
  DeliveryRoute.hasMany(PurchaseRequest, { foreignKey: 'delivery_route_id', as: 'purchase_requests' });

  // ============================================================================
  // MARKET INTEGRATION & PRICING
  // ============================================================================

  // Market contracts
  MarketContract.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketContractFarm' });
  MarketContract.belongsTo(Supplier, { foreignKey: 'supplier_id', as: 'marketContractSupplier' });
  MarketContract.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  Farm.hasMany(MarketContract, { foreignKey: 'farm_id', as: 'marketContracts' });
  Supplier.hasMany(MarketContract, { foreignKey: 'supplier_id', as: 'supplierContracts' });
  User.hasMany(MarketContract, { foreignKey: 'created_by', as: 'createdContracts', onDelete: 'CASCADE' });

  // Price comparison and analysis
  PriceComparison.belongsTo(Farm, { foreignKey: 'farm_id', as: 'priceComparisonFarm' });
  PriceComparison.belongsTo(Product, { foreignKey: 'product_id', as: 'priceComparisonProduct' });
  PriceComparison.belongsTo(Supplier, { foreignKey: 'best_price_supplier_id', as: 'bestPriceSupplier' });
  Farm.hasMany(PriceComparison, { foreignKey: 'farm_id', as: 'priceComparisons' });
  Product.hasMany(PriceComparison, { foreignKey: 'product_id', as: 'priceComparisons' });
  Supplier.hasMany(PriceComparison, { foreignKey: 'best_price_supplier_id', as: 'bestPriceComparisons' });

  // Market trends and analytics
  MarketTrend.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketTrendFarm' });
  MarketTrend.belongsTo(Product, { foreignKey: 'product_id', as: 'marketTrendProduct' });
  Farm.hasMany(MarketTrend, { foreignKey: 'farm_id', as: 'marketTrends' });
  Product.hasMany(MarketTrend, { foreignKey: 'product_id', as: 'marketTrends' });

  // Marketplace listings
  MarketplaceListing.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketplaceListingFarm' });
  MarketplaceListing.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  MarketplaceListing.belongsTo(Product, { foreignKey: 'product_id', as: 'marketplaceListingProduct' });
  Farm.hasMany(MarketplaceListing, { foreignKey: 'farm_id', as: 'marketplaceListings' });
  User.hasMany(MarketplaceListing, { foreignKey: 'created_by', as: 'createdListings', onDelete: 'CASCADE' });
  Product.hasMany(MarketplaceListing, { foreignKey: 'product_id', as: 'marketplaceListings' });

  // Shopping cart associations
  ShoppingCart.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  ShoppingCart.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  User.hasMany(ShoppingCart, { foreignKey: 'user_id', as: 'shoppingCarts' });
  Farm.hasMany(ShoppingCart, { foreignKey: 'farm_id', as: 'shoppingCarts' });

  // Shopping cart items
  ShoppingCart.hasMany(ShoppingCartItem, { foreignKey: 'cart_id', as: 'items' });
  ShoppingCartItem.belongsTo(ShoppingCart, { foreignKey: 'cart_id', as: 'cart' });
  ShoppingCartItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });
  Product.hasMany(ShoppingCartItem, { foreignKey: 'product_id', as: 'cartItems' });

  // Purchase request associations
  PurchaseRequest.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  PurchaseRequest.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  PurchaseRequest.belongsTo(Customer, { foreignKey: 'customer_id', as: 'customer' });
  User.hasMany(PurchaseRequest, { foreignKey: 'user_id', as: 'purchaseRequests' });
  Farm.hasMany(PurchaseRequest, { foreignKey: 'farm_id', as: 'purchaseRequests' });
  Customer.hasMany(PurchaseRequest, { foreignKey: 'customer_id', as: 'purchaseRequests' });

  // Farm Fulfillment Options associations
  Farm.hasMany(FarmFulfillmentOptions, { foreignKey: 'farm_id', as: 'fulfillmentOptions' });
  FarmFulfillmentOptions.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });

  // Farm Feature Toggles associations
  Farm.hasOne(FarmFeatureToggles, { foreignKey: 'farm_id', as: 'featureToggles' });
  FarmFeatureToggles.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });

  // Purchase request items
  PurchaseRequest.hasMany(PurchaseRequestItem, { foreignKey: 'request_id', as: 'items' });
  PurchaseRequestItem.belongsTo(PurchaseRequest, { foreignKey: 'request_id', as: 'request' });
  PurchaseRequestItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });
  Product.hasMany(PurchaseRequestItem, { foreignKey: 'product_id', as: 'purchaseRequestItems' });

  // Purchase request delivery address
  PurchaseRequest.belongsTo(CustomerAddress, { foreignKey: 'delivery_address_id', as: 'deliveryAddress' });
  CustomerAddress.hasMany(PurchaseRequest, { foreignKey: 'delivery_address_id', as: 'purchaseRequests' });

  // Price tracking (current, future, historical)
  MarketPrice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'marketPriceFarm' });
  Farm.hasMany(MarketPrice, { foreignKey: 'farm_id', as: 'marketPrices' });
  FuturePrice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'futurePriceFarm' });
  Farm.hasMany(FuturePrice, { foreignKey: 'farm_id', as: 'futurePrices' });
  HistoricalPrice.belongsTo(Farm, { foreignKey: 'farm_id', as: 'historicalPriceFarm' });
  Farm.hasMany(HistoricalPrice, { foreignKey: 'farm_id', as: 'historicalPrices' });

  // ============================================================================
  // TAX MANAGEMENT SYSTEM
  // ============================================================================

  // Tax categories and deductions
  TaxCategory.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxCategoryFarm' });
  Farm.hasMany(TaxCategory, { foreignKey: 'farm_id', as: 'taxCategories' });
  TaxDeduction.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxDeductionFarm' });
  Farm.hasMany(TaxDeduction, { foreignKey: 'farm_id', as: 'taxDeductions' });

  // Tax document management
  TaxDocument.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxDocumentFarm' });
  TaxDocument.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  Farm.hasMany(TaxDocument, { foreignKey: 'farm_id', as: 'taxDocuments' });
  User.hasMany(TaxDocument, { foreignKey: 'created_by', as: 'createdTaxDocuments', onDelete: 'CASCADE' });

  // Employee tax information (W-2 forms)
  EmployeeTaxInfo.belongsTo(Employee, { foreignKey: 'employee_id', as: 'employee' });
  EmployeeTaxInfo.belongsTo(Farm, { foreignKey: 'farm_id', as: 'employeeTaxInfoFarm' });
  EmployeeTaxInfo.belongsTo(TaxDocument, { foreignKey: 'w2_document_id', as: 'w2Document' });
  Employee.hasMany(EmployeeTaxInfo, { foreignKey: 'employee_id', as: 'taxInfo' });
  Farm.hasMany(EmployeeTaxInfo, { foreignKey: 'farm_id', as: 'employeeTaxInfo' });
  TaxDocument.hasOne(EmployeeTaxInfo, { foreignKey: 'w2_document_id', as: 'employeeTaxInfo' });

  // Contractor tax information (1099 forms)
  ContractorTaxInfo.belongsTo(Farm, { foreignKey: 'farm_id', as: 'contractorTaxInfoFarm' });
  ContractorTaxInfo.belongsTo(TaxDocument, { foreignKey: 'form_1099_document_id', as: 'form1099Document' });
  Farm.hasMany(ContractorTaxInfo, { foreignKey: 'farm_id', as: 'contractorTaxInfo' });
  TaxDocument.hasOne(ContractorTaxInfo, { foreignKey: 'form_1099_document_id', as: 'contractorTaxInfo' });

  // Tax payments and filings
  TaxPayment.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxPaymentFarm' });
  TaxPayment.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  TaxPayment.belongsTo(TaxDocument, { foreignKey: 'receipt_document_id', as: 'receiptDocument' });
  Farm.hasMany(TaxPayment, { foreignKey: 'farm_id', as: 'taxPayments' });
  User.hasMany(TaxPayment, { foreignKey: 'created_by', as: 'createdTaxPayments', onDelete: 'CASCADE' });
  TaxDocument.hasOne(TaxPayment, { foreignKey: 'receipt_document_id', as: 'taxPayment' });

  TaxFiling.belongsTo(Farm, { foreignKey: 'farm_id', as: 'taxFilingFarm' });
  TaxFiling.belongsTo(User, { foreignKey: 'created_by', as: 'creator', onDelete: 'CASCADE' });
  TaxFiling.belongsTo(TaxDocument, { foreignKey: 'document_id', as: 'filingDocument' });
  Farm.hasMany(TaxFiling, { foreignKey: 'farm_id', as: 'taxFilings' });
  User.hasMany(TaxFiling, { foreignKey: 'created_by', as: 'createdTaxFilings', onDelete: 'CASCADE' });
  TaxDocument.hasOne(TaxFiling, { foreignKey: 'document_id', as: 'taxFiling' });

  // ============================================================================
  // DOCUMENT SIGNING SYSTEM
  // ============================================================================

  // Signable document management
  SignableDocument.belongsTo(Farm, { foreignKey: 'farm_id', as: 'signableDocumentFarm' });
  SignableDocument.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  Farm.hasMany(SignableDocument, { foreignKey: 'farm_id', as: 'signableDocuments' });
  User.hasMany(SignableDocument, { foreignKey: 'created_by', as: 'createdSignableDocuments' });

  // Document signers and signatures
  SignableDocument.hasMany(DocumentSigner, { foreignKey: 'document_id', as: 'signers' });
  SignableDocument.hasMany(DocumentSignature, { foreignKey: 'document_id', as: 'signatures' });
  DocumentSigner.hasMany(DocumentSignature, { foreignKey: 'signer_id', as: 'signatures' });

  // Document fields and form data
  SignableDocument.hasMany(DocumentField, { foreignKey: 'document_id', as: 'fields' });
  DocumentSigner.hasMany(DocumentField, { foreignKey: 'signer_id', as: 'fields' });

  // Document audit trail
  DocumentAuditLog.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'auditLogDocument' });
  DocumentAuditLog.belongsTo(DocumentSigner, { foreignKey: 'signer_id', as: 'signer' });
  DocumentAuditLog.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  SignableDocument.hasMany(DocumentAuditLog, { foreignKey: 'document_id', as: 'auditLogs' });
  DocumentSigner.hasMany(DocumentAuditLog, { foreignKey: 'signer_id', as: 'auditLogs' });
  User.hasMany(DocumentAuditLog, { foreignKey: 'user_id', as: 'documentAuditLogs' });

  // Blockchain verification for document integrity
  DocumentBlockchainVerification.belongsTo(SignableDocument, { foreignKey: 'document_id', as: 'blockchainVerificationDocument' });
  SignableDocument.hasMany(DocumentBlockchainVerification, { foreignKey: 'document_id', as: 'blockchainVerifications' });

  // Digital certificates for signing
  DigitalCertificate.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  DigitalCertificate.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  User.hasMany(DigitalCertificate, { foreignKey: 'user_id', as: 'digitalCertificates' });
  Farm.hasMany(DigitalCertificate, { foreignKey: 'farm_id', as: 'digitalCertificates' });

  // ============================================================================
  // INVOICING & BILLING
  // ============================================================================

  // Invoice management
  Invoice.belongsTo(Customer, { foreignKey: 'customer_id' });
  Customer.hasMany(Invoice, { foreignKey: 'customer_id' });

  // Farm-to-farm invoicing
  Invoice.belongsTo(Farm, { foreignKey: 'recipient_farm_id', as: 'recipientFarm' });
  Farm.hasMany(Invoice, { foreignKey: 'recipient_farm_id', as: 'receivedInvoices' });

  // Invoice disputes
  InvoiceDispute.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice' });
  Invoice.hasMany(InvoiceDispute, { foreignKey: 'invoice_id', as: 'disputes' });

  InvoiceDispute.belongsTo(Farm, { foreignKey: 'raised_by_farm_id', as: 'raisedByFarm' });
  Farm.hasMany(InvoiceDispute, { foreignKey: 'raised_by_farm_id', as: 'raisedDisputes' });

  InvoiceDispute.belongsTo(User, { foreignKey: 'raised_by_user_id', as: 'raisedByUser' });
  User.hasMany(InvoiceDispute, { foreignKey: 'raised_by_user_id', as: 'raisedDisputes' });

  InvoiceDispute.belongsTo(User, { foreignKey: 'resolved_by_user_id', as: 'resolvedByUser' });
  User.hasMany(InvoiceDispute, { foreignKey: 'resolved_by_user_id', as: 'resolvedDisputes' });

  InvoiceDispute.belongsTo(User, { foreignKey: 'escalated_to_user_id', as: 'escalatedToUser' });
  User.hasMany(InvoiceDispute, { foreignKey: 'escalated_to_user_id', as: 'escalatedDisputes' });

  // Invoice dispute messages
  InvoiceDisputeMessage.belongsTo(InvoiceDispute, { foreignKey: 'dispute_id', as: 'dispute' });
  InvoiceDispute.hasMany(InvoiceDisputeMessage, { foreignKey: 'dispute_id', as: 'messages' });

  InvoiceDisputeMessage.belongsTo(User, { foreignKey: 'sender_user_id', as: 'senderUser' });
  User.hasMany(InvoiceDisputeMessage, { foreignKey: 'sender_user_id', as: 'disputeMessages' });

  InvoiceDisputeMessage.belongsTo(Farm, { foreignKey: 'sender_farm_id', as: 'senderFarm' });
  Farm.hasMany(InvoiceDisputeMessage, { foreignKey: 'sender_farm_id', as: 'disputeMessages' });

  // Invoice audit logs
  InvoiceAuditLog.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice' });
  Invoice.hasMany(InvoiceAuditLog, { foreignKey: 'invoice_id', as: 'auditLogs' });

  InvoiceAuditLog.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  User.hasMany(InvoiceAuditLog, { foreignKey: 'user_id', as: 'invoiceAuditLogs' });

  InvoiceAuditLog.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Farm.hasMany(InvoiceAuditLog, { foreignKey: 'farm_id', as: 'invoiceAuditLogs' });

  // Invoice notifications
  InvoiceNotification.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice' });
  Invoice.hasMany(InvoiceNotification, { foreignKey: 'invoice_id', as: 'notifications' });

  InvoiceNotification.belongsTo(User, { foreignKey: 'recipient_user_id', as: 'recipientUser' });
  User.hasMany(InvoiceNotification, { foreignKey: 'recipient_user_id', as: 'invoiceNotifications' });

  InvoiceNotification.belongsTo(Farm, { foreignKey: 'recipient_farm_id', as: 'recipientFarm' });
  Farm.hasMany(InvoiceNotification, { foreignKey: 'recipient_farm_id', as: 'receivedInvoiceNotifications' });

  InvoiceNotification.belongsTo(Farm, { foreignKey: 'sender_farm_id', as: 'senderFarm' });
  Farm.hasMany(InvoiceNotification, { foreignKey: 'sender_farm_id', as: 'sentInvoiceNotifications' });

  // Invoice line items
  InvoiceItem.belongsTo(Invoice, { foreignKey: 'invoice_id', as: 'invoice', onDelete: 'CASCADE' });
  Invoice.hasMany(InvoiceItem, { foreignKey: 'invoice_id', onDelete: 'CASCADE' });
  InvoiceItem.belongsTo(Product, { foreignKey: 'product_id', as: 'product' });
  Product.hasMany(InvoiceItem, { foreignKey: 'product_id', as: 'invoiceItems' });

  // Invoice payment tracking
  Invoice.belongsTo(Transaction, { foreignKey: 'payment_transaction_id', as: 'paymentTransaction' });
  Transaction.hasMany(Invoice, { foreignKey: 'payment_transaction_id', as: 'paidInvoices' });

  // Invoice questions
  InvoiceQuestion.belongsTo(Invoice, { foreignKey: 'invoice_id', onDelete: 'CASCADE' });
  Invoice.hasMany(InvoiceQuestion, { foreignKey: 'invoice_id', onDelete: 'CASCADE' });
  InvoiceQuestion.belongsTo(Customer, { foreignKey: 'customer_id' });

  // Customer notifications
  CustomerNotification.belongsTo(Customer, { foreignKey: 'customer_id' });
  Customer.hasMany(CustomerNotification, { foreignKey: 'customer_id' });
  CustomerNotification.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(CustomerNotification, { foreignKey: 'farm_id' });
  Customer.hasMany(InvoiceQuestion, { foreignKey: 'customer_id' });
  InvoiceQuestion.belongsTo(Farm, { foreignKey: 'farm_id' });
  Farm.hasMany(InvoiceQuestion, { foreignKey: 'farm_id' });

  // ============================================================================
  // PASSWORD MANAGER SYSTEM
  // ============================================================================

  // Password group associations
  PasswordGroup.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Farm.hasMany(PasswordGroup, { foreignKey: 'farm_id', as: 'passwordGroups' });
  PasswordGroup.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  User.hasMany(PasswordGroup, { foreignKey: 'created_by', as: 'createdPasswordGroups' });

  // Password associations
  Password.belongsTo(PasswordGroup, { foreignKey: 'group_id', as: 'group' });
  PasswordGroup.hasMany(Password, { foreignKey: 'group_id', as: 'passwords' });
  Password.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  User.hasMany(Password, { foreignKey: 'created_by', as: 'createdPasswords' });

  // Password group permissions
  PasswordGroupPermission.belongsTo(PasswordGroup, { foreignKey: 'group_id', as: 'group' });
  PasswordGroup.hasMany(PasswordGroupPermission, { foreignKey: 'group_id', as: 'permissions' });
  PasswordGroupPermission.belongsTo(Role, { foreignKey: 'role_id', as: 'role' });
  Role.hasMany(PasswordGroupPermission, { foreignKey: 'role_id', as: 'passwordGroupPermissions' });
  PasswordGroupPermission.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  User.hasMany(PasswordGroupPermission, { foreignKey: 'user_id', as: 'passwordGroupPermissions' });

  // User recovery keys
  UserRecoveryKey.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
  User.hasOne(UserRecoveryKey, { foreignKey: 'user_id', as: 'recoveryKey' });

  // ============================================================================
  // BILLING SYSTEM
  // ============================================================================

  // Bill category associations
  BillCategory.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Farm.hasMany(BillCategory, { foreignKey: 'farm_id', as: 'billCategories' });

  // Bill associations
  Bill.belongsTo(Farm, { foreignKey: 'farm_id', as: 'farm' });
  Farm.hasMany(Bill, { foreignKey: 'farm_id', as: 'bills' });
  Bill.belongsTo(BillCategory, { foreignKey: 'category_id', as: 'category' });
  BillCategory.hasMany(Bill, { foreignKey: 'category_id', as: 'bills' });
  Bill.belongsTo(Vendor, { foreignKey: 'vendor_id', as: 'vendor' });
  Vendor.hasMany(Bill, { foreignKey: 'vendor_id', as: 'bills' });
  Bill.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  User.hasMany(Bill, { foreignKey: 'created_by', as: 'createdBills' });

  // Recurring bill associations
  RecurringBill.belongsTo(Bill, { foreignKey: 'bill_id', as: 'bill' });
  Bill.hasOne(RecurringBill, { foreignKey: 'bill_id', as: 'recurringSchedule' });

  // Bill payment associations
  BillPayment.belongsTo(Bill, { foreignKey: 'bill_id', as: 'bill' });
  Bill.hasMany(BillPayment, { foreignKey: 'bill_id', as: 'payments' });
  BillPayment.belongsTo(User, { foreignKey: 'created_by', as: 'creator' });
  User.hasMany(BillPayment, { foreignKey: 'created_by', as: 'createdBillPayments' });

  // Bill transaction associations (linking bills to transactions)
  BillTransaction.belongsTo(Bill, { foreignKey: 'bill_id', as: 'bill' });
  Bill.hasMany(BillTransaction, { foreignKey: 'bill_id', as: 'transactions' });
  BillTransaction.belongsTo(Transaction, { foreignKey: 'transaction_id', as: 'transaction' });
  Transaction.hasMany(BillTransaction, { foreignKey: 'transaction_id', as: 'linkedBills' });
  BillTransaction.belongsTo(User, { foreignKey: 'linked_by', as: 'linkedBy' });
  User.hasMany(BillTransaction, { foreignKey: 'linked_by', as: 'linkedBillTransactions' });

  // Bill attachment associations
  BillAttachment.belongsTo(Bill, { foreignKey: 'bill_id', as: 'bill' });
  Bill.hasMany(BillAttachment, { foreignKey: 'bill_id', as: 'attachments' });
  BillAttachment.belongsTo(User, { foreignKey: 'uploaded_by', as: 'uploader' });
  User.hasMany(BillAttachment, { foreignKey: 'uploaded_by', as: 'uploadedBillAttachments' });
};
