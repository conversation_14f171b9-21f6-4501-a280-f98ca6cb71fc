import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import Farm from './Farm.js';

const DeliverySchedule = sequelize.define('delivery_schedules', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'farms',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  day_of_week: {
    type: DataTypes.INTEGER,
    allowNull: false,
    validate: {
      min: 0,
      max: 6
    },
    comment: '0 = Sunday, 1 = Monday, etc.'
  },
  start_time: {
    type: DataTypes.TIME,
    allowNull: false
  },
  end_time: {
    type: DataTypes.TIME,
    allowNull: false
  },
  max_deliveries: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  timestamps: false,
  underscored: true,
  tableName: 'delivery_schedules'
});

// Define associations
DeliverySchedule.belongsTo(Farm, { foreignKey: 'farm_id' });
// Note: The Farm.hasMany association is defined in associations.js

export default DeliverySchedule;
