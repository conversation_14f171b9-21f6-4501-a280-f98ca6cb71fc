import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import Crop from './Crop.js';
import Field from './Field.js';
import dotenv from 'dotenv';

dotenv.config();

const CropDisease = defineModel('CropDisease', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  crop_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Crop,
      key: 'id'
    }
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Field,
      key: 'id'
    },
    comment: 'Field ID if the prediction is for a specific field'
  },
  disease_name: {
    type: DataTypes.STRING(255),
    allowNull: false,
    comment: 'Name of the potential disease'
  },
  probability: {
    type: DataTypes.DECIMAL(5, 4),
    allowNull: false,
    comment: 'Probability of the disease occurring (0-1)'
  },
  recommended_actions: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Recommended actions to prevent or treat the disease'
  },
  risk_factors: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Factors that contribute to the disease risk'
  },
  prediction_date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'Date when the prediction was made'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'crop_diseases',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
CropDisease.belongsTo(Farm, { foreignKey: 'farm_id' });
Farm.hasMany(CropDisease, { foreignKey: 'farm_id' });

CropDisease.belongsTo(Crop, { foreignKey: 'crop_id' });
Crop.hasMany(CropDisease, { foreignKey: 'crop_id' });

CropDisease.belongsTo(Field, { foreignKey: 'field_id' });
Field.hasMany(CropDisease, { foreignKey: 'field_id' });

export default CropDisease;