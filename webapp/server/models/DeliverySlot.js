import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import DeliverySchedule from './DeliverySchedule.js';

const DeliverySlot = sequelize.define('delivery_slots', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  schedule_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'delivery_schedules',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  slot_time: {
    type: DataTypes.TIME,
    allowNull: false
  },
  max_deliveries: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  timestamps: false,
  underscored: true,
  tableName: 'delivery_slots'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default DeliverySlot;