import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import dotenv from 'dotenv';

dotenv.config();

const Invoice = defineModel('Invoice', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'The farm that created/issued the invoice'
  },
  recipient_farm_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'The farm that is receiving the invoice (for farm-to-farm invoicing)'
  },
  customer_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'customers',
      key: 'id'
    },
    comment: 'The customer receiving the invoice (used when recipient is not a farm)'
  },
  invoice_number: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  issue_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  due_date: {
    type: DataTypes.DATEONLY,
    allowNull: false
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: false,
    defaultValue: 'draft'
  },
  subtotal: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false
  },
  tax_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    defaultValue: 0
  },
  total_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  payment_transaction_id: {
    type: DataTypes.UUID,
    allowNull: true,
    comment: 'Reference to the transaction record for this payment'
  },
  payment_method: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Method used for payment (e.g., credit_card, ach, etc.)'
  },
  payment_date: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When the payment was made'
  },
  payment_amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true,
    comment: 'Amount paid (may differ from total_amount if partially paid)'
  },
  stripe_payment_intent_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Stripe payment intent ID for this invoice payment'
  },
  stripe_payment_method_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Stripe payment method ID used for this invoice payment'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'invoices',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
Invoice.belongsTo(Farm, { foreignKey: 'farm_id' });
Farm.hasMany(Invoice, { foreignKey: 'farm_id' });

// Note: Associations with Customer and InvoiceItem are defined in associations.js

export default Invoice;
