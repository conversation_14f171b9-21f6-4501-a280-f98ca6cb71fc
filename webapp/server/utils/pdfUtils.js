import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import Invoice from '../models/Invoice.js';
import InvoiceItem from '../models/InvoiceItem.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';

/**
 * Generate a PDF buffer for an invoice
 * 
 * @param {string} invoiceId - The ID of the invoice
 * @returns {Promise<Buffer>} - A buffer containing the PDF data
 */
export const generateInvoicePdfBuffer = async (invoiceId) => {
  try {
    // Find the invoice with all related data
    const invoice = await Invoice.findByPk(invoiceId, {
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
        },
        {
          model: Farm,
          attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
        },
        {
          model: Farm,
          as: 'recipientFarm',
          attributes: ['id', 'name', 'billing_email', 'phone', 'address', 'city', 'state', 'zip_code', 'country', 'primary_contact_name', 'primary_contact_email', 'primary_contact_phone', 'payment_terms']
        },
        {
          model: InvoiceItem,
          attributes: ['id', 'description', 'quantity', 'unit_price', 'amount']
        }
      ]
    });

    if (!invoice) {
      throw new Error('Invoice not found');
    }

    // Create a new PDF document
    const pdfDoc = await PDFDocument.create();

    // Add a page to the document
    const page = pdfDoc.addPage([612, 792]); // Letter size

    // Get fonts
    const helveticaFont = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const helveticaBold = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Set font sizes
    const titleFontSize = 24;
    const headerFontSize = 14;
    const normalFontSize = 12;
    const smallFontSize = 10;

    // Set margins
    const margin = 50;
    const width = page.getWidth() - 2 * margin;

    // Draw invoice header
    page.drawText('INVOICE', {
      x: margin,
      y: 720,
      size: titleFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    // Draw invoice number and dates
    page.drawText(`Invoice #: ${invoice.invoice_number}`, {
      x: margin,
      y: 690,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    page.drawText(`Issue Date: ${new Date(invoice.issue_date).toLocaleDateString()}`, {
      x: margin,
      y: 670,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    page.drawText(`Due Date: ${new Date(invoice.due_date).toLocaleDateString()}`, {
      x: margin,
      y: 650,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    // Draw status
    const statusText = invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1);
    page.drawText(`Status: ${statusText}`, {
      x: margin,
      y: 630,
      size: normalFontSize,
      font: helveticaBold,
      color: invoice.status === 'paid' ? rgb(0, 0.5, 0) : rgb(0, 0, 0),
    });

    // Draw from (farm) information
    page.drawText('From:', {
      x: margin,
      y: 590,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    if (invoice.Farm) {
      page.drawText(invoice.Farm.name, {
        x: margin,
        y: 570,
        size: normalFontSize,
        font: helveticaFont,
        color: rgb(0, 0, 0),
      });

      let yPos = 550;
      
      if (invoice.Farm.address) {
        page.drawText(invoice.Farm.address, {
          x: margin,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      let cityStateZip = '';
      if (invoice.Farm.city) cityStateZip += invoice.Farm.city;
      if (invoice.Farm.state) cityStateZip += cityStateZip ? `, ${invoice.Farm.state}` : invoice.Farm.state;
      if (invoice.Farm.zip_code) cityStateZip += cityStateZip ? ` ${invoice.Farm.zip_code}` : invoice.Farm.zip_code;

      if (cityStateZip) {
        page.drawText(cityStateZip, {
          x: margin,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      if (invoice.Farm.country) {
        page.drawText(invoice.Farm.country, {
          x: margin,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      if (invoice.Farm.phone) {
        page.drawText(`Phone: ${invoice.Farm.phone}`, {
          x: margin,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      if (invoice.Farm.billing_email) {
        page.drawText(`Email: ${invoice.Farm.billing_email}`, {
          x: margin,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }
    }

    // Draw to (customer) information
    page.drawText('To:', {
      x: 350,
      y: 590,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    if (invoice.Customer) {
      page.drawText(invoice.Customer.name, {
        x: 350,
        y: 570,
        size: normalFontSize,
        font: helveticaFont,
        color: rgb(0, 0, 0),
      });

      let yPos = 550;
      
      if (invoice.Customer.address) {
        page.drawText(invoice.Customer.address, {
          x: 350,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      let cityStateZip = '';
      if (invoice.Customer.city) cityStateZip += invoice.Customer.city;
      if (invoice.Customer.state) cityStateZip += cityStateZip ? `, ${invoice.Customer.state}` : invoice.Customer.state;
      if (invoice.Customer.zip_code) cityStateZip += cityStateZip ? ` ${invoice.Customer.zip_code}` : invoice.Customer.zip_code;

      if (cityStateZip) {
        page.drawText(cityStateZip, {
          x: 350,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      if (invoice.Customer.country) {
        page.drawText(invoice.Customer.country, {
          x: 350,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      if (invoice.Customer.phone) {
        page.drawText(`Phone: ${invoice.Customer.phone}`, {
          x: 350,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      if (invoice.Customer.email) {
        page.drawText(`Email: ${invoice.Customer.email}`, {
          x: 350,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }
    } else if (invoice.recipientFarm) {
      // If the recipient is another farm
      page.drawText(invoice.recipientFarm.name, {
        x: 350,
        y: 570,
        size: normalFontSize,
        font: helveticaFont,
        color: rgb(0, 0, 0),
      });

      let yPos = 550;
      
      if (invoice.recipientFarm.address) {
        page.drawText(invoice.recipientFarm.address, {
          x: 350,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      let cityStateZip = '';
      if (invoice.recipientFarm.city) cityStateZip += invoice.recipientFarm.city;
      if (invoice.recipientFarm.state) cityStateZip += cityStateZip ? `, ${invoice.recipientFarm.state}` : invoice.recipientFarm.state;
      if (invoice.recipientFarm.zip_code) cityStateZip += cityStateZip ? ` ${invoice.recipientFarm.zip_code}` : invoice.recipientFarm.zip_code;

      if (cityStateZip) {
        page.drawText(cityStateZip, {
          x: 350,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      if (invoice.recipientFarm.country) {
        page.drawText(invoice.recipientFarm.country, {
          x: 350,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      if (invoice.recipientFarm.phone) {
        page.drawText(`Phone: ${invoice.recipientFarm.phone}`, {
          x: 350,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
        yPos -= 15;
      }

      if (invoice.recipientFarm.billing_email) {
        page.drawText(`Email: ${invoice.recipientFarm.billing_email}`, {
          x: 350,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });
      }
    }

    // Draw invoice items table header
    const tableTop = 450;
    const tableRowHeight = 25;
    const descriptionWidth = 250;
    const quantityWidth = 70;
    const priceWidth = 100;
    const amountWidth = 100;

    // Draw table header
    page.drawText('Description', {
      x: margin,
      y: tableTop,
      size: normalFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    page.drawText('Quantity', {
      x: margin + descriptionWidth,
      y: tableTop,
      size: normalFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    page.drawText('Unit Price', {
      x: margin + descriptionWidth + quantityWidth,
      y: tableTop,
      size: normalFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    page.drawText('Amount', {
      x: margin + descriptionWidth + quantityWidth + priceWidth,
      y: tableTop,
      size: normalFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    // Draw horizontal line below header
    page.drawLine({
      start: { x: margin, y: tableTop - 10 },
      end: { x: margin + descriptionWidth + quantityWidth + priceWidth + amountWidth, y: tableTop - 10 },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // Draw invoice items
    let yPos = tableTop - 30;
    if (invoice.InvoiceItems && invoice.InvoiceItems.length > 0) {
      for (const item of invoice.InvoiceItems) {
        // Draw item description
        page.drawText(item.description || 'No description', {
          x: margin,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
          maxWidth: descriptionWidth - 10,
        });

        // Draw item quantity
        page.drawText(item.quantity.toString(), {
          x: margin + descriptionWidth,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });

        // Draw item unit price
        page.drawText(`$${parseFloat(item.unit_price).toFixed(2)}`, {
          x: margin + descriptionWidth + quantityWidth,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });

        // Draw item amount
        page.drawText(`$${parseFloat(item.amount).toFixed(2)}`, {
          x: margin + descriptionWidth + quantityWidth + priceWidth,
          y: yPos,
          size: smallFontSize,
          font: helveticaFont,
          color: rgb(0, 0, 0),
        });

        yPos -= tableRowHeight;

        // Add a new page if we're running out of space
        if (yPos < 100) {
          page = pdfDoc.addPage([612, 792]);
          yPos = 700;
        }
      }
    } else {
      page.drawText('No items', {
        x: margin,
        y: yPos,
        size: smallFontSize,
        font: helveticaFont,
        color: rgb(0, 0, 0),
      });
      yPos -= tableRowHeight;
    }

    // Draw horizontal line below items
    page.drawLine({
      start: { x: margin, y: yPos + 10 },
      end: { x: margin + descriptionWidth + quantityWidth + priceWidth + amountWidth, y: yPos + 10 },
      thickness: 1,
      color: rgb(0, 0, 0),
    });

    // Draw totals
    yPos -= 20;
    
    // Draw subtotal
    page.drawText('Subtotal:', {
      x: margin + descriptionWidth + quantityWidth,
      y: yPos,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    page.drawText(`$${parseFloat(invoice.subtotal || 0).toFixed(2)}`, {
      x: margin + descriptionWidth + quantityWidth + priceWidth,
      y: yPos,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    // Draw tax
    yPos -= 20;
    page.drawText('Tax:', {
      x: margin + descriptionWidth + quantityWidth,
      y: yPos,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    page.drawText(`$${parseFloat(invoice.tax_amount || 0).toFixed(2)}`, {
      x: margin + descriptionWidth + quantityWidth + priceWidth,
      y: yPos,
      size: normalFontSize,
      font: helveticaFont,
      color: rgb(0, 0, 0),
    });

    // Draw total
    yPos -= 30;
    page.drawText('Total:', {
      x: margin + descriptionWidth + quantityWidth,
      y: yPos,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    page.drawText(`$${parseFloat(invoice.total_amount || 0).toFixed(2)}`, {
      x: margin + descriptionWidth + quantityWidth + priceWidth,
      y: yPos,
      size: headerFontSize,
      font: helveticaBold,
      color: rgb(0, 0, 0),
    });

    // Draw notes
    if (invoice.notes) {
      yPos -= 50;
      page.drawText('Notes:', {
        x: margin,
        y: yPos,
        size: normalFontSize,
        font: helveticaBold,
        color: rgb(0, 0, 0),
      });

      yPos -= 20;
      page.drawText(invoice.notes, {
        x: margin,
        y: yPos,
        size: smallFontSize,
        font: helveticaFont,
        color: rgb(0, 0, 0),
        maxWidth: width,
      });
    }

    // Serialize the PDF to bytes
    const pdfBytes = await pdfDoc.save();
    
    return Buffer.from(pdfBytes);
  } catch (error) {
    console.error('Error generating invoice PDF buffer:', error);
    throw error;
  }
};