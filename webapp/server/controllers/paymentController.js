import Invoice from '../models/Invoice.js';
import InvoiceItem from '../models/InvoiceItem.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import { v4 as uuidv4 } from 'uuid';

// Payment method interface
const paymentMethods = new Map();

// Get all invoices for a farm
export const getFarmInvoices = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get all invoices for the farm
    const invoices = await Invoice.findAll({
      where: { farm_id: farmId },
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email']
        }
      ],
      order: [['issue_date', 'DESC']]
    });

    return res.status(200).json({ invoices });
  } catch (error) {
    console.error('Error getting farm invoices:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all payment methods for a farm
export const getFarmPaymentMethods = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // In a real implementation, this would fetch payment methods from a database or payment provider
    // For now, we'll return a mock list of payment methods
    const mockPaymentMethods = [
      {
        id: '4d9141d5-df79-4e3c-9423-ca342bd6cbcc',
        type: 'card',
        last4: '4242',
        brand: 'Visa',
        expMonth: 12,
        expYear: 2025,
        isDefault: true
      },
      {
        id: '5e8f7a2c-6b1d-4f3e-8a2d-9b3c4d5e6f7a',
        type: 'card',
        last4: '1234',
        brand: 'Mastercard',
        expMonth: 10,
        expYear: 2024,
        isDefault: false
      }
    ];

    // Store the payment methods in memory for retrieval by ID
    mockPaymentMethods.forEach(method => {
      paymentMethods.set(method.id, method);
    });

    return res.status(200).json({ paymentMethods: mockPaymentMethods });
  } catch (error) {
    console.error('Error getting farm payment methods:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a specific payment method by ID
export const getPaymentMethod = async (req, res) => {
  try {
    const { paymentMethodId } = req.params;

    // In a real implementation, this would fetch the payment method from a database or payment provider
    // For now, we'll return a mock payment method if the ID matches
    const paymentMethod = paymentMethods.get(paymentMethodId);

    if (!paymentMethod) {
      return res.status(404).json({ 
        message: "The requested resource could not be found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: `/payments/methods/${paymentMethodId}`,
          method: "GET"
        }
      });
    }

    return res.status(200).json({ paymentMethod });
  } catch (error) {
    console.error('Error getting payment method:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single invoice by ID for a farm
export const getInvoiceById = async (req, res) => {
  try {
    const { farmId, invoiceId } = req.params;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Find invoice for the specified farm
    const invoice = await Invoice.findOne({
      where: {
        id: invoiceId,
        farm_id: farmId
      },
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
        },
        {
          model: InvoiceItem,
          attributes: ['id', 'description', 'quantity', 'unit_price', 'amount']
        }
      ]
    });

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    return res.status(200).json({ invoice });
  } catch (error) {
    console.error('Error getting invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single invoice by ID without requiring a farm ID
export const getInvoiceByIdOnly = async (req, res) => {
  try {
    const { invoiceId } = req.params;

    // Find invoice by ID without farm restriction
    const invoice = await Invoice.findOne({
      where: {
        id: invoiceId
      },
      include: [
        {
          model: Customer,
          attributes: ['id', 'name', 'email', 'phone', 'address', 'city', 'state', 'zip_code', 'country']
        },
        {
          model: InvoiceItem,
          attributes: ['id', 'description', 'quantity', 'unit_price', 'amount']
        }
      ]
    });

    if (!invoice) {
      return res.status(404).json({ 
        message: "The requested resource could not be found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: `/payments/invoices/${invoiceId}`,
          method: "GET"
        }
      });
    }

    return res.status(200).json({ invoice });
  } catch (error) {
    console.error('Error getting invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a new payment method for a farm
export const createPaymentMethod = async (req, res) => {
  try {
    const { farmId } = req.params;
    const paymentMethodData = req.body;

    // Find farm to ensure it exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Validate required fields
    if (!paymentMethodData.type) {
      return res.status(400).json({ error: 'Payment method type is required' });
    }

    // Generate a unique ID for the new payment method
    const newPaymentMethodId = uuidv4();

    // Create a new payment method object
    const newPaymentMethod = {
      id: newPaymentMethodId,
      ...paymentMethodData,
      // Add default values if needed
      isDefault: paymentMethodData.isDefault || false
    };

    // In a real implementation, this would save the payment method to a database or payment provider
    // For now, we'll store it in our in-memory map
    paymentMethods.set(newPaymentMethodId, newPaymentMethod);

    return res.status(201).json({ paymentMethod: newPaymentMethod });
  } catch (error) {
    console.error('Error creating payment method:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update a payment method by ID
export const updatePaymentMethod = async (req, res) => {
  try {
    const { paymentMethodId } = req.params;
    const updateData = req.body;

    // In a real implementation, this would update the payment method in a database or payment provider
    // For now, we'll check if the payment method exists in our mock data
    const paymentMethod = paymentMethods.get(paymentMethodId);

    if (!paymentMethod) {
      return res.status(404).json({ 
        message: "The requested resource could not be found",
        errorType: "NotFoundError",
        errorCode: "404",
        context: {
          url: `/payments/methods/${paymentMethodId}`,
          method: "POST"
        }
      });
    }

    // Validate payment method type if it's being updated
    if ('type' in updateData && !updateData.type) {
      return res.status(400).json({ error: 'Payment method type is required' });
    }

    // Update the payment method with the new data
    const updatedMethod = { ...paymentMethod, ...updateData };
    paymentMethods.set(paymentMethodId, updatedMethod);

    return res.status(200).json({ paymentMethod: updatedMethod });
  } catch (error) {
    console.error('Error updating payment method:', error);
    return res.status(500).json({ error: error.message });
  }
};
