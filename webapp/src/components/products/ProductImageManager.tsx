import React, { useState, useEffect, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { 
  uploadProductImage, 
  getProductImages, 
  reorderProductImages, 
  deleteProductImage, 
  setPrimaryImage,
  ProductImage 
} from '../../services/productImageService';

interface ProductImageManagerProps {
  productId: string;
  readOnly?: boolean;
}

const ProductImageManager: React.FC<ProductImageManagerProps> = ({ 
  productId, 
  readOnly = false 
}) => {
  const [images, setImages] = useState<ProductImage[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch product images
  const fetchImages = useCallback(async () => {
    if (!productId) return;

    setLoading(true);
    try {
      const fetchedImages = await getProductImages(productId);
      setImages(fetchedImages);
    } catch (err) {
      console.error('Error fetching product images:', err);
      setError('Failed to load images. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, [productId]);

  useEffect(() => {
    fetchImages();
  }, [fetchImages]);

  // Handle file drop
  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    if (!productId || readOnly) return;

    setUploading(true);
    setError(null);

    try {
      // Upload each file
      for (const file of acceptedFiles) {
        await uploadProductImage(productId, file);
      }

      // Refresh images
      await fetchImages();
    } catch (err) {
      console.error('Error uploading images:', err);
      setError('Failed to upload images. Please try again later.');
    } finally {
      setUploading(false);
    }
  }, [productId, readOnly, fetchImages]);

  // Set up dropzone
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.webp']
    },
    disabled: readOnly || uploading
  });

  // Handle image reordering
  const handleDragEnd = async (result: any) => {
    if (!result.destination || readOnly) return;

    const reorderedImages = Array.from(images);
    const [movedImage] = reorderedImages.splice(result.source.index, 1);
    reorderedImages.splice(result.destination.index, 0, movedImage);

    // Update local state immediately for better UX
    setImages(reorderedImages);

    // Send reorder request to server
    try {
      const imageIds = reorderedImages.map(img => img.id);
      await reorderProductImages(productId, imageIds);
    } catch (err) {
      console.error('Error reordering images:', err);
      setError('Failed to reorder images. Please try again later.');
      // Revert to original order on error
      fetchImages();
    }
  };

  // Handle image deletion
  const handleDeleteImage = async (imageId: string) => {
    if (readOnly) return;

    if (!window.confirm('Are you sure you want to delete this image?')) {
      return;
    }

    try {
      await deleteProductImage(productId, imageId);
      // Update local state
      setImages(images.filter(img => img.id !== imageId));
    } catch (err) {
      console.error('Error deleting image:', err);
      setError('Failed to delete image. Please try again later.');
    }
  };

  // Handle setting primary image
  const handleSetPrimary = async (imageId: string) => {
    if (readOnly) return;

    try {
      await setPrimaryImage(productId, imageId);
      // Update local state
      setImages(images.map(img => ({
        ...img,
        is_primary: img.id === imageId
      })));
    } catch (err) {
      console.error('Error setting primary image:', err);
      setError('Failed to set primary image. Please try again later.');
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">Product Images</h3>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      {/* Image upload dropzone */}
      {!readOnly && (
        <div
          {...getRootProps()}
          className={`mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md ${
            isDragActive ? 'border-primary-500 bg-primary-50' : 'border-gray-300'
          } ${uploading || readOnly ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
        >
          <input {...getInputProps()} />
          <div className="space-y-1 text-center">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              stroke="currentColor"
              fill="none"
              viewBox="0 0 48 48"
              aria-hidden="true"
            >
              <path
                d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
            <div className="flex text-sm text-gray-600 justify-center">
              <label className="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                <span>Upload images</span>
              </label>
              <p className="pl-1">or drag and drop</p>
            </div>
            <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
            {uploading && (
              <div className="flex items-center justify-center mt-2">
                <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-primary-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="text-sm text-primary-500">Uploading...</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Image gallery */}
      {loading ? (
        <div className="flex justify-center items-center h-40">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          <p className="ml-2 text-gray-500">Loading images...</p>
        </div>
      ) : images.length > 0 ? (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="product-images" direction="horizontal">
            {(provided) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4"
              >
                {images.map((image, index) => (
                  <Draggable
                    key={image.id}
                    draggableId={image.id}
                    index={index}
                    isDragDisabled={readOnly}
                  >
                    {(provided) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                        className={`relative group border rounded-md overflow-hidden ${
                          image.is_primary ? 'ring-2 ring-primary-500' : ''
                        }`}
                      >
                        <img
                          src={image.url || image.file_path}
                          alt={`Product image ${index + 1}`}
                          className="w-full h-32 object-cover"
                        />
                        {!readOnly && (
                          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                            {!image.is_primary && (
                              <button
                                onClick={() => handleSetPrimary(image.id)}
                                className="p-1 bg-blue-500 rounded-full text-white hover:bg-blue-600"
                                title="Set as primary image"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                              </button>
                            )}
                            <button
                              onClick={() => handleDeleteImage(image.id)}
                              className="p-1 bg-red-500 rounded-full text-white hover:bg-red-600"
                              title="Delete image"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                            </button>
                          </div>
                        )}
                        {image.is_primary && (
                          <div className="absolute top-1 left-1 bg-primary-500 text-white text-xs px-1 rounded">
                            Primary
                          </div>
                        )}
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      ) : (
        <div className="text-center py-4 border border-gray-200 rounded-md">
          <p className="text-gray-500">No images have been added to this product yet.</p>
          {!readOnly && (
            <p className="text-sm text-gray-400 mt-1">
              Upload images using the dropzone above.
            </p>
          )}
        </div>
      )}

      {images.length > 0 && !readOnly && (
        <p className="text-sm text-gray-500 mt-2">
          Drag and drop images to reorder them. Click the star icon to set an image as primary.
        </p>
      )}
    </div>
  );
};

export default ProductImageManager;
