import React, { useEffect, Children, isValidElement, cloneElement } from 'react';
import Layout from '../Layout';
import MarketplaceHeader from '../marketplace/MarketplaceHeader';
import MarketplaceFooter from '../marketplace/MarketplaceFooter';

interface MarketplaceThemeWrapperProps {
  children: React.ReactNode;
}

const MarketplaceThemeWrapper: React.FC<MarketplaceThemeWrapperProps> = ({ children }) => {
  // Load the marketing-site theme CSS when the component mounts
  useEffect(() => {
    // Create a link element for the marketing-site CSS
    const marketingThemeLink = document.createElement('link');
    marketingThemeLink.rel = 'stylesheet';
    marketingThemeLink.href = '/marketing-site-theme.css';
    marketingThemeLink.id = 'marketing-site-theme';

    // Add it to the document head
    document.head.appendChild(marketingThemeLink);

    // Remove the marketing-site theme CSS when the component unmounts
    return () => {
      const themeLink = document.getElementById('marketing-site-theme');
      if (themeLink) {
        document.head.removeChild(themeLink);
      }
    };
  }, []);

  // Process children to remove Layout components and extract their children
  const processChildren = (child: React.ReactNode): React.ReactNode => {
    // If the child is a Layout component, extract its children
    if (isValidElement(child) && child.type === Layout) {
      const props = child.props as { children?: React.ReactNode };
      return props.children;
    }

    // If the child has children of its own, process them recursively
    if (isValidElement(child)) {
      const props = child.props as { children?: React.ReactNode };
      if (props.children !== undefined) {
        return cloneElement(
          child as React.ReactElement<any>,
          {},
          Children.map(props.children, processChildren)
        );
      }
    }

    // Otherwise, return the child as is
    return child;
  };

  const processedChildren = Children.map(children, processChildren);

  return (
    <div className="marketing-site-theme">
      <MarketplaceHeader />
      {processedChildren}
      <MarketplaceFooter />
    </div>
  );
};

export default MarketplaceThemeWrapper;
